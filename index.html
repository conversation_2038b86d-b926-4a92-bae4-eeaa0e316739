<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Load Room01</title>
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <h2>Izvēlies procesa stadiju (progress ir sadalīts atsevišķās telpās):</h2>

    <hr />
    <button onclick="loadRoom1()">Room01</button>
    <p>
      Izveidota pārvietošanās un telpas loģika – var uzzīmēt karti "room01.txt"
      failā, ko iesauc pie mainLogic kā <b>data-map=""</b>.
    </p>
    <p>
      Nākamā telpa tiek iesaukta pie mainLogic skripta kā
      <b>next-link="room02.html"</b>
    </p>
    <p>Ka<PERSON>s apz<PERSON>ju<PERSON>:</p>
    <p><b>x</b> – gr<PERSON>da;</p>
    <p><b>o</b> – siena;</p>
    <p>
      <b>v, &lt;, &gt;, ^</b> – gr<PERSON><PERSON> la<PERSON>, kur tiek novietots spēlētājs, +
      virziens, kurā tas pavērsts;
    </p>
    <p><b>y</b> – caurums grīdā;</p>
    <p><b>z</b> – izeja;</p>

    <hr />
    <button onclick="loadRoom2()">Room02</button>
    <p>
      Pievienota poga, kuru nospiežot, tiek iespējota izeja. Jānorāda pogai
      <b>id="exit-button"</b>.
    </p>

    <hr />
    <button onclick="loadRoom3()">Room03</button>
    <p>
      Pievienots GLB fails, kas nomaina pagaidu sienu pret konkrētu 3D modeli,
      ko iesauc A-frame ainā kā <b>id="wall-o"</b>.
    </p>
    <p>
      Exit pogai ir pievienota animācija, ko iesauc A-frame ainā kā
      <b>animation__move</b>.
    </p>

    <hr />
    <button onclick="loadRoom4()">Room04</button>
    <p>
      Pievienota loģika, ar kuru var pārvietot kartes lauciņus, samainot tos
      vietām. Kartē tos apzīmē ar cipariem no 1-9 (lai vēlāk piešķirtu citu GLB
      modeli). A-frame ainā to norāda kā <b>id="floor-1"</b> (cipars apzīmē, uz
      kuru lauciņu kartē tas attiecas). Koordinātes tiek ņemtas no aktuālā TXT
      faila kartes, kur pirmais skaitlis ir kolonna un otrais skaitlis ir rinda.
      Šādi <b>switch="(3, 1) to (3, 0)"</b> norāda, no kurienes uz kurieni tiks
      pārvietots attiecīgais lauciņš.
    </p>

    <hr />
    <button onclick="loadRoom5()">Room05</button>
    <p>
      Te ir pielāgots mainLogic kods, ļaujot spēlētājam tikt pārvietotam kopā ar
      kustīgo lauciņu.
    </p>

    <hr />
    <button onclick="loadRoom6()">Room06</button>
    <p>
      Pievienoju <b>animation__moveout</b> un <b>animation__moveback</b>, kas
      maina pogas lokāciju pirms un pēc lauciņa kustināšanas. Pievienoju miglu
      (mazāku par 24 distanci nevajadzētu izmantot).
    </p>

    <hr />
    <button onclick="loadRoom7()">Room07</button>
    <p>
      Pievienoju GLB objektus sienām un grīdām. Jaunajā kodā var grīdas apzīmēt
      ar "1" līdz "5" un sienas ar "a" līdz "e". Iesauc pie asset-item kā
      <b>wall-a</b> vai <b>floor-1</b>.
    </p>

    <hr />
    <button onclick="loadRoom8()">Room08</button>
    <p>
      Pievienoju paceļamus un noliekamus elementus ko apzīmē ar
      <b>class="pickable-large"</b>.
    </p>

    <hr />
    <button onclick="loadRoom9()">Room09</button>
    <p>
      Pievienoju <b>switch-plate="(x, y) to (x, y)"</b>, kas reaģē uz lieliem
      objektiem (<b>class="pickable-large"</b>).
    </p>

    <hr />
    <button onclick="loadRoom10()">Room10</button>
    <p>
      Pievienoju <b> object-plate="(x, y) to (x, y)"</b>, kas reaģē uz maziem
      objektiem (<b>class="pickable-small"</b>), bet tad abiem nepieciešams
      pievienot <b>connect="nosaukums"</b>, kur abiem ir viens un tas pats
      nosaukums. Pieveinoju arī lauciņu <b>q</b>, kas ir lauciņs, uz kura var
      uzlikt objektu, bet nevar uzkāpt.
    </p>

    <hr />
    <button onclick="loadRoom11()">Room11</button>
    <p>
      Pievienoju jaunu skriptu
      <b>
        "https://cdn.jsdelivr.net/gh/donmccurdy/aframe-extras@v6.1.1/dist/aframe-extras.min.js"</b
      >, kas ļauj iekustināt GLB failu animācijas, ierakstot pie aseta
      <b>animation-mixer</b>. Pievienoju <b>renderdistance=""</b>, ko iesauc pie a-scene – tas jāizmanto tikai tad, ja jums ir pārāk liela karte un raustās spēle.
    </p>
    <button onclick="loadRoom12()">Room12</button>
    <p>
      Pievienoju jaunu skriptu, kas ļauj iekustināt GLB failu animācijas pārvietojamam objektam, te ir nepieciešams sagatavot gan a-asset-item (norāda animate="") gan a-entity (norāda pozīciju (nevis koordinātis), animation-mixer un follow="").
    </p>

    <script>
      function loadRoom1() {
        window.location.href = "room01.html";
      }
      function loadRoom2() {
        window.location.href = "room02.html";
      }
      function loadRoom3() {
        window.location.href = "room03.html";
      }
      function loadRoom4() {
        window.location.href = "room04.html";
      }
      function loadRoom5() {
        window.location.href = "room05.html";
      }
      function loadRoom6() {
        window.location.href = "room06.html";
      }
      function loadRoom7() {
        window.location.href = "room07.html";
      }
      function loadRoom8() {
        window.location.href = "room08.html";
      }
      function loadRoom9() {
        window.location.href = "room09.html";
      }
      function loadRoom10() {
        window.location.href = "room10.html";
      }
      function loadRoom11() {
        window.location.href = "room11.html";
      }
      function loadRoom12() {
        window.location.href = "room12.html";
      }
    </script>
  </body>
</html>
