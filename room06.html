<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Block Movement Example</title>
    <script src="https://aframe.io/releases/1.6.0/aframe.min.js"></script>
  </head>
  <body>
    <a-scene
      cursor="rayOrigin: mouse"
      fog="type: linear; color: #AAA; near: 0; far: 24"
    >
      <a-box
        id="exit-button"
        position="8 0 4"
        color="red"
        animation__move="property: position;
                   to: 8 -0.25 4;
                   dur: 1000;
                   easing: easeInOutQuad;
                   autoplay: false"
      ></a-box>
      <a-box
        position="16 0 3"
        color="pink"
        switch="(3, 1) to (3, 0)"
        animation__moveout="property: position;
                               to: 16 0 5;
                               dur: 1000;
                               easing: easeInOutQuad;
                               autoplay: false"
        animation__moveback="property: position;
                   to: 16 0 3;
                   dur: 1000;
                   easing: easeInOutQuad;          
                   autoplay: false"
      >
      </a-box>
      <a-box
        position="15 0 8"
        color="pink"
        switch="(4, 1) to (5, 1)"
        animation__moveout="property: position;
                               to: 17 0 8;
                               dur: 1000;
                               easing: easeInOutQuad;
                               autoplay: false"
        animation__moveback="property: position;
                   to: 15 0 8;
                   dur: 1000;
                   easing: easeInOutQuad;          
                   autoplay: false"
      >
      </a-box>
      <a-entity
        gltf-model="https://cdn.glitch.global/1c490d13-0c87-48ec-aac0-3f731c19e451/vase.glb?v=1736098742893"
        position="24 1 4"
      ></a-entity>
      <a-sky color="lightblue"></a-sky>
      <a-assets>
        <a-asset-item
          id="wall-o"
          src="https://cdn.glitch.global/1c490d13-0c87-48ec-aac0-3f731c19e451/box_wall.glb?v=1735939307042"
        ></a-asset-item>
      </a-assets>
    </a-scene>
    <script
      src="mainLogic.js"
      data-map="room06.txt"
      next-link="room07.html"
    ></script>
  </body>
</html>
