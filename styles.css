/* General Styles */
body {
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  background-color: #f4f4f9;
  color: #333;
}

/* Container Styles */
body {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #ddd;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Header */
h2 {
  font-size: 1.5em;
  color: #4CAF50;
  text-align: center;
  margin-bottom: 20px;
}

/* Paragraphs */
p {
  margin: 0 0 10px;
  padding-left: 15px;
  text-indent: -15px;
}

/* HR <PERSON>yling */
hr {
  margin: 20px 0;
  border: none;
  border-top: 1px solid #ddd;
}

/* Buttons */
button {
  display: block;
  width: 100%;
  max-width: 200px;
  margin: 10px auto;
  padding: 10px;
  font-size: 16px;
  color: #ffffff;
  background-color: #4CAF50;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

button:hover {
  background-color: #45a049;
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* Indented List of Map Details */
p b {
  font-weight: bold;
  color: #4CAF50;
}

/* Responsive Design */
@media (max-width: 600px) {
  body {
    padding: 10px;
  }

  button {
    font-size: 14px;
  }

  h2 {
    font-size: 1.3em;
  }
}
