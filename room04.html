<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Block Movement Example</title>
    <script src="https://aframe.io/releases/1.6.0/aframe.min.js"></script>
  </head>
  <body>
    <a-scene cursor="rayOrigin: mouse">
      <a-box
        id="exit-button"
        position="8 0 4"
        color="red"
        animation__move="property: position;
                   to: 8 -0.25 4;
                   dur: 1000;
                   easing: easeInOutQuad;
                   autoplay: false"
      ></a-box>
      <a-box position="16 0 4" color="pink" switch="(3, 1) to (3, 0)"> </a-box>
      <a-sky color="lightblue"></a-sky>
      <a-assets>
        <a-asset-item
          id="wall-o"
          src="https://cdn.glitch.global/1c490d13-0c87-48ec-aac0-3f731c19e451/box_wall.glb?v=1735939307042"
        ></a-asset-item>
      </a-assets>
    </a-scene>
    <script
      src="mainLogic.js"
      data-map="room04.txt"
      next-link="room05.html"
    ></script>
  </body>
</html>
