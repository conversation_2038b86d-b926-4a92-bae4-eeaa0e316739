// ---------------------------------------------------
// A) CONFIG & GLOBALS
// ---------------------------------------------------
let mapData = [];

const thisScript = document.currentScript;
const sceneEl = document.querySelector("a-scene");
const mapFile = thisScript.getAttribute("data-map") || "room05.txt"; // Use provided file or default
const linkFile = thisScript.getAttribute("next-link") || "index.html"; // Use provided link or default

const tileSize = 4;
const tileSpacing = 0.01;
const floorHeight = 0.2;
const wallHeight = 3.0;

let exitEnabled = false;
let directionIndex = 0;
let startPosition = { col: 0, row: 0 };
let heldItemClasses = [];

const directions = [
  { row: 1, col: 0 },
  { row: 0, col: 1 },
  { row: -1, col: 0 },
  { row: 0, col: -1 },
];

let cameraRig, cameraEl;
const rotationQueue = [];
let isRotating = false;

let isHolding = false;
let heldItemModel = null; // Holds the VISUAL model (animated if follower exists)
let heldItemStaticModel = null; // Holds the FUNCTIONAL model (static)
let heldItemConnect = null;
let heldItemAnimate = null; // Holds the 'animate' identifier if item has a follower
let heldItemSize = "large";
let heldEntity = null;

const occupantMap = {}; // Map: "col,row" -> large functional pickable entity
const plateMap = {}; // Map: "col,row" -> plate functional entity

let renderDistance = Infinity;
const rdAttr = sceneEl.getAttribute("renderdistance");
if (rdAttr !== null) {
  const rd = parseInt(rdAttr, 10);
  if (rd > 0) renderDistance = rd;
}

// ---------------------------------------------------
// B) WAIT FOR DOM, THEN LOAD MAP & BUILD SCENE
// ---------------------------------------------------
document.addEventListener("DOMContentLoaded", () => {
  if (!sceneEl) {
    console.error("No <a-scene> found!");
    return;
  }

  const exitBtn = document.querySelector("#exit-button");
  if (exitBtn) {
    exitEnabled = false;
    exitBtn.addEventListener("click", () => {
      exitEnabled = true;
      console.log("Exit enabled!");
      exitBtn.setAttribute("animation__move", "autoplay", "true");
    });
  } else {
    exitEnabled = true;
  }

  fetch(mapFile)
    .then((resp) => resp.text())
    .then((text) => {
      mapData = text.trim().split("\n");
      generateMap();
      createCameraRig();
      placeCameraInstantly();
      setupCoordinateBasedObjects(); // Create functional entities (pickables, plates) from assets
      setupSwitchLogic(); // *** Add listeners to static switches in HTML ***
      setupPickupSystem();
      setupPlateLogic(); // Primarily setupCombinedPlateCheck relies on plateMap populated above
      setupCombinedPlateCheck();

      setTimeout(() => {
        syncAllFollowers(); // Position followers, show them, hide functional targets
        updateRenderCulling(); // Initial culling update after sync
      }, 100);

      setupKeyControls();
      setupTouchControls();
    })
    .catch((err) => console.error("Failed to load map from", mapFile, err));
});

// ---------------------------------------------------
// 1) GENERATE MAP (Floors, Walls - unchanged)
// ---------------------------------------------------
function generateMap() {
  const numRows = mapData.length;
  const numCols = mapData[0].length;
  for (let row = 0; row < numRows; row++) {
    for (let col = 0; col < numCols; col++) {
      const tile = mapData[row][col];
      const posX = col * (tileSize + tileSpacing);
      const posZ = row * (tileSize + tileSpacing);
      let entity = null;
      let modelId = null;
      if ("oabcdef".includes(tile)) {
        modelId = `#wall-${tile}`;
        if (document.querySelector(modelId)) {
          entity = document.createElement("a-entity");
          entity.setAttribute("gltf-model", modelId);
          entity.setAttribute("position", { x: posX, y: 0, z: posZ });
        } else {
          entity = document.createElement("a-box");
          entity.setAttribute("width", tileSize);
          entity.setAttribute("height", wallHeight);
          entity.setAttribute("depth", tileSize);
          entity.setAttribute("color", "#444");
          entity.setAttribute("position", {
            x: posX,
            y: wallHeight / 2,
            z: posZ,
          });
        }
      } else if (tile === "y") {
        continue;
      } else if (tile === "z") {
        modelId = "#floor-z";
        if (!document.querySelector(modelId)) modelId = "#floor";
        entity = document.createElement(
          document.querySelector(modelId) ? "a-entity" : "a-box"
        );
        if (document.querySelector(modelId))
          entity.setAttribute("gltf-model", modelId);
        else {
          entity.setAttribute("width", tileSize);
          entity.setAttribute("height", floorHeight);
          entity.setAttribute("depth", tileSize);
          entity.setAttribute("color", "#0f0");
        }
        entity.setAttribute("position", {
          x: posX,
          y: floorHeight / 2,
          z: posZ,
        });
      } else if ("123456789".includes(tile)) {
        modelId = `#floor-${tile}`;
        if (!document.querySelector(modelId)) modelId = "#floor";
        entity = document.createElement(
          document.querySelector(modelId) ? "a-entity" : "a-box"
        );
        if (document.querySelector(modelId))
          entity.setAttribute("gltf-model", modelId);
        else {
          entity.setAttribute("width", tileSize);
          entity.setAttribute("height", floorHeight);
          entity.setAttribute("depth", tileSize);
          entity.setAttribute("color", "#888");
        }
        entity.setAttribute("position", {
          x: posX,
          y: floorHeight / 2,
          z: posZ,
        });
      } else if (tile === "q") {
        modelId = `#floor-q`;
        if (!document.querySelector(modelId)) modelId = "#floor";
        entity = document.createElement(
          document.querySelector(modelId) ? "a-entity" : "a-box"
        );
        if (document.querySelector(modelId))
          entity.setAttribute("gltf-model", modelId);
        else {
          entity.setAttribute("width", tileSize);
          entity.setAttribute("height", floorHeight);
          entity.setAttribute("depth", tileSize);
          entity.setAttribute("color", "#888");
        }
        entity.setAttribute("position", {
          x: posX,
          y: floorHeight / 2,
          z: posZ,
        });
        entity.setAttribute("non-walkable", "true");
      } else {
        modelId = "#floor";
        entity = document.createElement(
          document.querySelector(modelId) ? "a-entity" : "a-box"
        );
        if (document.querySelector(modelId))
          entity.setAttribute("gltf-model", modelId);
        else {
          entity.setAttribute("width", tileSize);
          entity.setAttribute("height", floorHeight);
          entity.setAttribute("depth", tileSize);
          entity.setAttribute("color", "#888");
        }
        entity.setAttribute("position", {
          x: posX,
          y: floorHeight / 2,
          z: posZ,
        });
        if ("^>v<".includes(tile)) {
          startPosition = { col: col, row: row };
          directionIndex = "^>v<".indexOf(tile);
        }
      }
      if (entity) {
        entity.setAttribute("data-map-coord", `${col},${row}`);
        sceneEl.appendChild(entity);
      }
    }
  }
}

// ---------------------------------------------------
// 2) CREATE CAMERA RIG (Unchanged)
// ---------------------------------------------------
function createCameraRig() {
  cameraRig = document.createElement("a-entity");
  sceneEl.appendChild(cameraRig);
  cameraEl = document.createElement("a-entity");
  cameraEl.setAttribute("camera", "active: true");
  cameraEl.setAttribute("look-controls", "enabled: false");
  cameraEl.setAttribute("position", "0 1.6 0");
  cameraRig.appendChild(cameraEl);
  cameraRig.addEventListener("animationcomplete", onRotationComplete);
}
function placeCameraInstantly() {
  const posX = startPosition.col * (tileSize + tileSpacing);
  const posZ = startPosition.row * (tileSize + tileSpacing);
  cameraRig.setAttribute("position", { x: posX, y: 0, z: posZ });
  cameraRig.setAttribute("rotation", { x: 0, y: directionIndex * 90, z: 0 });
}
function animatePosition(toX, toY, toZ, dur = 500) {
  cameraRig.removeAttribute("animation__pos");
  cameraRig.setAttribute("animation__pos", {
    property: "position",
    to: `${toX} ${toY} ${toZ}`,
    dur: dur,
    easing: "easeInOutQuad",
  });
}

// ---------------------------------------------------
// 3) MOVEMENT + RELATIVE TURN (Keyboard - Unchanged)
// ---------------------------------------------------
function setupKeyControls() {
  document.addEventListener("keydown", (evt) => {
    const key = evt.key.toLowerCase();
    if (key === "d" || key === "arrowright") {
      directionIndex = (directionIndex + 3) % 4;
      rotateRig(-90);
    } else if (key === "a" || key === "arrowleft") {
      directionIndex = (directionIndex + 1) % 4;
      rotateRig(90);
    } else if (key === "w" || key === "arrowup") {
      moveForward();
    } else if (key === "s" || key === "arrowdown") {
      moveBackward();
    }
  });
}

// ---------------------------------------------------
// 4) SWIPE LOGIC FOR PHONES (Unchanged)
// ---------------------------------------------------
function setupTouchControls() {
  let touchStartX = 0,
    touchStartY = 0;
  document.addEventListener(
    "touchstart",
    (evt) => {
      if (evt.touches.length > 1) return;
      touchStartX = evt.touches[0].clientX;
      touchStartY = evt.touches[0].clientY;
    },
    { passive: true }
  );
  document.addEventListener(
    "touchend",
    (evt) => {
      if (evt.changedTouches.length === 0) return;
      const diffX = evt.changedTouches[0].clientX - touchStartX;
      const diffY = evt.changedTouches[0].clientY - touchStartY;
      const threshold = 50;
      if (Math.abs(diffX) > Math.abs(diffY)) {
        if (diffX > threshold) {
          directionIndex = (directionIndex + 3) % 4;
          rotateRig(-90);
        } else if (diffX < -threshold) {
          directionIndex = (directionIndex + 1) % 4;
          rotateRig(90);
        }
      } else {
        if (diffY < -threshold) {
          moveForward();
        } else if (diffY > threshold) {
          moveBackward();
        }
      }
    },
    { passive: true }
  );
}

// ---------------------------------------------------
// 5) HELPER: MOVE FORWARD / BACK (Unchanged)
// ---------------------------------------------------
function moveForward() {
  const oldCol = startPosition.col,
    oldRow = startPosition.row;
  const newCol = oldCol - directions[directionIndex].col,
    newRow = oldRow - directions[directionIndex].row;
  if (canWalkOn(newCol, newRow)) {
    startPosition = { col: newCol, row: newRow };
    animatePosition(
      newCol * (tileSize + tileSpacing),
      0,
      newRow * (tileSize + tileSpacing)
    );
    updatePlateState(oldCol, oldRow);
    updatePlateState(newCol, newRow);
    updateRenderCulling();
  }
}
function moveBackward() {
  const oldCol = startPosition.col,
    oldRow = startPosition.row;
  const newCol = oldCol + directions[directionIndex].col,
    newRow = oldRow + directions[directionIndex].row;
  if (canWalkOn(newCol, newRow)) {
    startPosition = { col: newCol, row: newRow };
    animatePosition(
      newCol * (tileSize + tileSpacing),
      0,
      newRow * (tileSize + tileSpacing)
    );
    updatePlateState(oldCol, oldRow);
    updatePlateState(newCol, newRow);
    updateRenderCulling();
  }
}

// ---------------------------------------------------
// 6) ROTATION QUEUE (Unchanged)
// ---------------------------------------------------
function rotateRig(byAngle) {
  rotationQueue.push(byAngle);
  processNextRotation();
}
function processNextRotation() {
  if (isRotating || rotationQueue.length === 0) return;
  isRotating = true;
  const byAngle = rotationQueue.shift();
  const oldY = parseFloat(cameraRig.getAttribute("rotation")?.y) || 0;
  const newY = oldY + byAngle;
  cameraRig.removeAttribute("animation__rot");
  cameraRig.setAttribute("animation__rot", {
    property: "rotation.y",
    from: oldY,
    to: newY,
    dur: 500,
    easing: "easeInOutQuad",
  });
  cameraRig.setAttribute("data-final-rotation", newY);
}
function onRotationComplete(evt) {
  if (evt.detail?.name === "animation__rot") {
    const finalY = cameraRig.getAttribute("data-final-rotation");
    if (finalY !== null)
      cameraRig.setAttribute("rotation", { x: 0, y: parseFloat(finalY), z: 0 });
    isRotating = false;
    processNextRotation();
  }
}

// ---------------------------------------------------
// 7) COLLISION & EXIT LOGIC (Unchanged)
// ---------------------------------------------------
function canWalkOn(col, row) {
  if (row < 0 || row >= mapData.length || col < 0 || col >= mapData[0].length)
    return false;
  const tile = mapData[row][col];
  if (occupantMap[col + "," + row]) return false;
  if ("oabcdeyq".includes(tile)) return false;
  if (tile === "z" && exitEnabled) {
    window.location.href = linkFile;
    return false;
  }
  return true;
}
function canPlaceOn(col, row, forLarge) {
  if (row < 0 || row >= mapData.length || col < 0 || col >= mapData[0].length)
    return false;
  const tile = mapData[row][col];
  if ("oabcdeyz".includes(tile)) return false;
  if (forLarge && occupantMap[col + "," + row]) return false;
  return true;
}

// ---------------------------------------------------
// 8) TILE SWAP LOGIC (Unchanged)
// ---------------------------------------------------
function swapTiles(fromCol, fromRow, toCol, toRow) {
  if (
    fromRow < 0 ||
    fromRow >= mapData.length ||
    fromCol < 0 ||
    fromCol >= mapData[0].length ||
    toRow < 0 ||
    toRow >= mapData.length ||
    toCol < 0 ||
    toCol >= mapData[0].length
  ) {
    console.error(
      `Invalid coords for swap: (${fromCol},${fromRow}) to (${toCol},${toRow})`
    );
    return;
  }
  const fromTile = mapData[fromRow][fromCol];
  const toTile = mapData[toRow][toCol];
  console.log(
    `Swapping tiles: (${fromCol},${fromRow})[${fromTile}] <-> (${toCol},${toRow})[${toTile}]`
  );
  const fromKey = `${fromCol},${fromRow}`;
  const toKey = `${toCol},${toRow}`;
  const fromPickables = document.querySelectorAll(
    `.pickable-small[data-map-coord="${fromKey}"], .pickable-large[data-map-coord="${fromKey}"]`
  );
  const toPickables = document.querySelectorAll(
    `.pickable-small[data-map-coord="${toKey}"], .pickable-large[data-map-coord="${toKey}"]`
  );
  mapData[fromRow] = replaceAt(mapData[fromRow], fromCol, toTile);
  mapData[toRow] = replaceAt(mapData[toRow], toCol, fromTile);
  swapOccupants(fromCol, fromRow, toCol, toRow);
  const animDuration = 1000;
  const fromPos = getTilePosition(fromCol, fromRow);
  const toPos = getTilePosition(toCol, toRow);
  const fromEntities = document.querySelectorAll(
    `[data-map-coord="${fromKey}"]`
  );
  const toEntities = document.querySelectorAll(`[data-map-coord="${toKey}"]`);
  fromEntities.forEach((entity) => {
    if (!plateMap[fromKey] || entity !== plateMap[fromKey]) {
      entity.removeAttribute("animation__position");
      entity.setAttribute("animation__position", {
        property: "position",
        to: `${toPos.x} ${entity.object3D.position.y} ${toPos.z}`,
        dur: animDuration,
        easing: "easeInOutQuad",
      });
      entity.setAttribute("data-map-coord", toKey);
    } else {
      console.log(`SwapTiles: Skipping animation for plate at ${fromKey}`);
    }
  });
  toEntities.forEach((entity) => {
    if (!plateMap[toKey] || entity !== plateMap[toKey]) {
      entity.removeAttribute("animation__position");
      entity.setAttribute("animation__position", {
        property: "position",
        to: `${fromPos.x} ${entity.object3D.position.y} ${fromPos.z}`,
        dur: animDuration,
        easing: "easeInOutQuad",
      });
      entity.setAttribute("data-map-coord", fromKey);
    } else {
      console.log(`SwapTiles: Skipping animation for plate at ${toKey}`);
    }
  });
  fromPickables.forEach((pickable) => {
    if (!pickable.hasAttribute("animation__position")) {
      pickable.removeAttribute("animation__pos");
      pickable.setAttribute("animation__pos", {
        property: "position",
        to: `${toPos.x} ${floorHeight} ${toPos.z}`,
        dur: animDuration,
        easing: "easeInOutQuad",
      });
    }
    pickable.setAttribute("data-map-coord", toKey);
    const followerId = pickable.getAttribute("animate");
    if (followerId)
      animateFollower(
        followerId,
        { x: toPos.x, y: floorHeight, z: toPos.z },
        animDuration
      );
  });
  toPickables.forEach((pickable) => {
    if (!pickable.hasAttribute("animation__position")) {
      pickable.removeAttribute("animation__pos");
      pickable.setAttribute("animation__pos", {
        property: "position",
        to: `${fromPos.x} ${floorHeight} ${fromPos.z}`,
        dur: animDuration,
        easing: "easeInOutQuad",
      });
    }
    pickable.setAttribute("data-map-coord", fromKey);
    const followerId = pickable.getAttribute("animate");
    if (followerId)
      animateFollower(
        followerId,
        { x: fromPos.x, y: floorHeight, z: fromPos.z },
        animDuration
      );
  });
  const playerPos = cameraRig.getAttribute("position");
  const pCol = Math.round(playerPos.x / (tileSize + tileSpacing));
  const pRow = Math.round(playerPos.z / (tileSize + tileSpacing));
  if (pCol === fromCol && pRow === fromRow) {
    animatePosition(toPos.x, 0, toPos.z, animDuration);
    startPosition = { col: toCol, row: toRow };
  } else if (pCol === toCol && pRow === toRow) {
    animatePosition(fromPos.x, 0, fromPos.z, animDuration);
    startPosition = { col: fromCol, row: toRow };
  }
  setTimeout(() => {
    console.log(`Post-swap check: Plate at ${fromKey}?`, plateMap[fromKey]);
    console.log(`Post-swap check: Plate at ${toKey}?`, plateMap[toKey]);
    updatePlateState(fromCol, fromRow);
    updatePlateState(toCol, toRow);
  }, animDuration + 100);
}
function swapOccupants(fromCol, fromRow, toCol, toRow) {
  const fromKey = fromCol + "," + fromRow,
    toKey = toCol + "," + toRow;
  const occA = occupantMap[fromKey],
    occB = occupantMap[toKey];
  if (occA) occupantMap[toKey] = occA;
  else delete occupantMap[toKey];
  if (occB) occupantMap[fromKey] = occB;
  else delete occupantMap[fromKey];
}
function replaceAt(str, index, replacement) {
  if (index >= str.length || index < 0) return str;
  return str.substring(0, index) + replacement + str.substring(index + 1);
}
function getTilePosition(col, row) {
  return {
    x: col * (tileSize + tileSpacing),
    y: 0,
    z: row * (tileSize + tileSpacing),
  };
}

// ---------------------------------------------------
// 9) SWITCH LOGIC (Updated Selector and Logging)
// ---------------------------------------------------
function setupSwitchLogic() {
  // Handles switches defined statically in HTML (if any) - More specific selector
  const staticSwitches = document.querySelectorAll(
    "a-box[switch], a-entity[switch]:not([switch-plate]):not([object-plate])"
  ); // Find boxes or entities with 'switch' but NOT plates
  console.log(`Found ${staticSwitches.length} static switch elements.`); // Log how many are found

  staticSwitches.forEach((switchEl) => {
    console.log("Attaching click listener to static switch:", switchEl); // Log attachment
    let isToggled = false; // State variable (might not be needed for simple swap)
    switchEl.addEventListener("click", (evt) => {
      // Added evt parameter
      console.log("Static switch clicked!", switchEl); // Log click event
      evt.stopPropagation(); // Prevent click bubbling further just in case

      const switchData = switchEl.getAttribute("switch");
      const match = switchData.match(
        /\(\s*(\d+)\s*,\s*(\d+)\s*\)\s*to\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)/
      );

      if (match) {
        // Only proceed if match is found
        const [, fromCol, fromRow, toCol, toRow] = match.map(Number);
        console.log(
          `Static Switch Action: Swapping (${fromCol},${fromRow}) and (${toCol},${toRow})`
        );
        swapTiles(fromCol, fromRow, toCol, toRow); // Call the swap function

        // Optional: Toggle switch animation if defined
        if (
          switchEl.hasAttribute("animation__moveout") &&
          switchEl.hasAttribute("animation__moveback")
        ) {
          switchEl.setAttribute(
            isToggled ? "animation__moveback" : "animation__moveout",
            "autoplay",
            "true"
          );
          switchEl.setAttribute(
            isToggled ? "animation__moveout" : "animation__moveback",
            "autoplay",
            "false"
          );
          isToggled = !isToggled;
        }
      } else {
        console.warn(`Static switch format error or no match: "${switchData}"`);
      }
    });
  });
  // NOTE: Plate logic handles switches defined via assets (switch-plate/object-plate)
}

// ---------------------------------------------------
// 10) PICKUP/PLACE LOGIC (Unchanged)
// ---------------------------------------------------
function setupPickupSystem() {
  sceneEl.addEventListener("click", (evt) => {
    let targetEl = evt.target;
    while (targetEl && targetEl !== sceneEl) {
      if (
        targetEl.matches &&
        targetEl.matches(".pickable-large, .pickable-small")
      ) {
        if (!isHolding) tryPickupEvent(targetEl);
        evt.stopPropagation();
        return;
      }
      if (
        targetEl.isMesh &&
        targetEl.parent?.el?.matches(".pickable-large, .pickable-small")
      ) {
        if (!isHolding) tryPickupEvent(targetEl.parent.el);
        evt.stopPropagation();
        return;
      }
      targetEl = targetEl.parentElement;
    }
  });
}
function tryPickupEvent(itemEl) {
  if (!itemEl?.object3D) return;
  const playerPos = cameraRig.object3D.position;
  const itemPos = itemEl.object3D.position;
  const pickupRadius = tileSize * 1.1;
  if (playerPos.distanceTo(itemPos) > pickupRadius) {
    console.log("Too far!");
    return;
  }
  pickUpSceneItem(itemEl);
}
function pickUpSceneItem(itemEl) {
  const tileCoord = itemEl.getAttribute("data-map-coord");
  const functionalModel = itemEl.getAttribute("gltf-model");
  const animateId = itemEl.getAttribute("animate");
  const connectId = itemEl.getAttribute("connect");
  const itemId = itemEl.id;
  let visualModel = functionalModel;
  let followerEl = null;
  if (animateId) {
    followerEl = sceneEl.querySelector(`a-entity[follow="${animateId}"]`);
    if (followerEl) {
      const followerModel = followerEl.getAttribute("gltf-model");
      if (followerModel) visualModel = followerModel;
      else console.warn(`Follower ${animateId} has no gltf-model.`);
      hideFollower(animateId);
    } else console.warn(`Item animate="${animateId}" but no follower found.`);
  }
  if (tileCoord) {
    const [col, row] = tileCoord.split(",").map(Number);
    if (
      itemEl.classList.contains("pickable-large") &&
      occupantMap[tileCoord] === itemEl
    )
      delete occupantMap[tileCoord];
    updatePlateState(col, row);
  }
  heldItemModel = visualModel;
  heldItemStaticModel = functionalModel;
  heldItemConnect = connectId;
  heldItemAnimate = animateId;
  heldItemSize = itemEl.classList.contains("pickable-small")
    ? "small"
    : "large";
  heldItemClasses = Array.from(itemEl.classList).filter(
    (cls) => !cls.startsWith("pickable-")
  );
  itemEl.remove();
  heldEntity = createHeldEntity(
    visualModel,
    heldItemStaticModel,
    itemId,
    connectId,
    animateId
  );
  isHolding = true;
  console.log(
    `Picked up: Visual=${visualModel || "N/A"}, Static=${
      functionalModel || "N/A"
    }, Size=${heldItemSize}, Connect=${connectId || "N/A"}, Animate=${
      animateId || "N/A"
    }`
  );
}
function createHeldEntity(
  visualModelSelector,
  staticModelSelector,
  itemId,
  itemConnect,
  itemAnimate
) {
  const entity = document.createElement("a-entity");
  if (visualModelSelector)
    entity.setAttribute("gltf-model", visualModelSelector);
  else {
    entity.setAttribute(
      "geometry",
      "primitive: box; width: 0.2; height: 0.2; depth: 0.2"
    );
    entity.setAttribute("material", "color: magenta");
    console.warn("Creating held entity placeholder");
  }
  entity.setAttribute("position", "0 -0.2 -1");
  entity.setAttribute("scale", "0.25 0.25 0.25");
  entity.setAttribute("rotation", "0 0 0");
  entity.setAttribute("data-size", heldItemSize);
  if (staticModelSelector)
    entity.setAttribute("data-static-model", staticModelSelector);
  if (itemId) entity.setAttribute("id", itemId);
  if (itemConnect) entity.setAttribute("connect", itemConnect);
  if (itemAnimate) entity.setAttribute("animate", itemAnimate);
  heldItemClasses.forEach((cls) => entity.classList.add(cls));
  entity.addEventListener("click", () => {
    placeHeldItem(entity);
  });
  cameraEl.appendChild(entity);
  if (
    itemAnimate &&
    visualModelSelector &&
    visualModelSelector !== staticModelSelector
  ) {
    setTimeout(() => {
      if (!entity.components["animation-mixer"])
        entity.setAttribute("animation-mixer", "");
    }, 200);
  }
  return entity;
}
function placeHeldItem(heldEl) {
  const targetCol = startPosition.col - directions[directionIndex].col;
  const targetRow = startPosition.row - directions[directionIndex].row;
  const isSmall = heldItemSize === "small";
  if (!canPlaceOn(targetCol, targetRow, !isSmall)) {
    console.log(
      `Cannot place ${heldItemSize} item at (${targetCol}, ${targetRow}).`
    );
    return;
  }
  const staticModelToUse = heldEl.getAttribute("data-static-model");
  const originalId = heldEl.getAttribute("id");
  const originalConnect = heldEl.getAttribute("connect");
  const originalAnimate = heldEl.getAttribute("animate");
  const newEntity = document.createElement("a-entity");
  if (!staticModelToUse) {
    console.error("Cannot place: Static model info missing!");
    heldEl.remove();
    isHolding = false;
    heldEntity = null;
    /* reset other vars*/ return;
  }
  newEntity.setAttribute("gltf-model", staticModelToUse);
  const tilePos = getTilePosition(targetCol, targetRow);
  newEntity.setAttribute("position", {
    x: tilePos.x,
    y: floorHeight,
    z: tilePos.z,
  });
  newEntity.setAttribute("scale", "1 1 1");
  newEntity.setAttribute("rotation", "0 0 0");
  if (originalId) newEntity.setAttribute("id", originalId);
  if (originalConnect) newEntity.setAttribute("connect", originalConnect);
  if (originalAnimate) newEntity.setAttribute("animate", originalAnimate);
  newEntity.classList.add(isSmall ? "pickable-small" : "pickable-large");
  newEntity.setAttribute("data-size", heldItemSize);
  heldItemClasses.forEach((cls) => newEntity.classList.add(cls));
  newEntity.setAttribute("data-map-coord", `${targetCol},${targetRow}`);
  sceneEl.appendChild(newEntity);
  heldEl.remove();
  if (!isSmall) {
    occupantMap[targetCol + "," + targetRow] = newEntity;
    console.log(`Added large item to occupantMap at ${targetCol},${targetRow}`);
  }
  if (originalAnimate) {
    newEntity.setAttribute("visible", "false");
    console.log(
      `Placed functional item ${originalAnimate}, ensuring it's hidden.`
    );
    setTimeout(() => {
      const follower = sceneEl.querySelector(
        `a-entity[follow="${originalAnimate}"]`
      );
      if (follower) {
        syncFollower(follower, newEntity);
      } else {
        console.warn(`Placed item ${originalAnimate}, but follower not found.`);
        newEntity.setAttribute("visible", "true");
      }
    }, 75);
  } else {
    newEntity.setAttribute("visible", "true");
  }
  updatePlateState(targetCol, targetRow);
  isHolding = false;
  heldItemModel = null;
  heldItemStaticModel = null;
  heldItemConnect = null;
  heldItemAnimate = null;
  heldItemSize = "large";
  heldItemClasses = [];
  heldEntity = null;
  console.log(`Placed item at (${targetCol}, ${targetRow})`);
}

// ---------------------------------------------------
// 10b) SPAWN OBJECTS FROM <a-asset-item> (Unchanged)
// ---------------------------------------------------
function setupCoordinateBasedObjects() {
  const coordEls = document.querySelectorAll("a-asset-item[coordinates]");
  console.log(`Found ${coordEls.length} asset items with coordinates.`);
  coordEls.forEach((el, index) => {
    const coords = el.getAttribute("coordinates");
    const modelSource = el.getAttribute("gltf-model") || el.getAttribute("src");
    const connectAttr = el.getAttribute("connect");
    const animateAttr = el.getAttribute("animate");
    const idAttr = el.getAttribute("id");
    if (!coords) {
      console.warn(`Asset item ${index} missing coordinates.`);
      return;
    }
    let [col, row] = coords.split(",").map((s) => parseInt(s.trim()));
    if (isNaN(row) || isNaN(col)) {
      console.warn(`Asset item ${index} invalid coordinates: "${coords}"`);
      return;
    }
    const entity = document.createElement("a-entity");
    if (modelSource) entity.setAttribute("gltf-model", modelSource);
    const pos = getTilePosition(col, row);
    entity.setAttribute("position", { x: pos.x, y: floorHeight, z: pos.z });
    entity.setAttribute("scale", "1 1 1");
    entity.setAttribute("data-map-coord", `${col},${row}`);
    if (connectAttr) entity.setAttribute("connect", connectAttr);
    if (animateAttr) entity.setAttribute("animate", animateAttr);
    let isPickable = false;
    let isSmall = false;
    if (
      el.classList.contains("pickable-large") ||
      el.classList.contains("pickable-small")
    ) {
      isPickable = true;
      isSmall = el.classList.contains("pickable-small");
      entity.classList.add(isSmall ? "pickable-small" : "pickable-large");
      entity.setAttribute("data-size", isSmall ? "small" : "large");
      if (!isSmall) occupantMap[col + "," + row] = entity;
      if (!modelSource)
        console.warn(`Pickable entity (${col},${row}) no model.`);
    }
    const switchPlateAttr = el.getAttribute("switch-plate");
    const objectPlateAttr = el.getAttribute("object-plate");
    if (switchPlateAttr) {
      entity.setAttribute("switch-plate", switchPlateAttr);
      plateMap[col + "," + row] = entity;
      console.log(`Created switch plate at ${col},${row}`);
    } else if (objectPlateAttr) {
      entity.setAttribute("object-plate", objectPlateAttr);
      if (connectAttr) {
        plateMap[col + "," + row] = entity;
        console.log(
          `Created object plate for '${connectAttr}' at ${col},${row}`
        );
      } else console.warn(`Object plate (${col},${row}) missing 'connect'.`);
    }
    if (idAttr && !document.querySelector(`#${idAttr}`))
      entity.setAttribute("id", idAttr);
    el.classList.forEach((cls) => {
      if (
        !cls.startsWith("pickable-") &&
        cls !== "a-asset-item" &&
        !entity.classList.contains(cls)
      )
        entity.classList.add(cls);
    });
    entity.setAttribute("visible", !(isPickable && animateAttr));
    sceneEl.appendChild(entity);
  });
  console.log("Finished setupCoordinateBasedObjects.");
  for (const key in plateMap) {
    const [col, row] = key.split(",").map(Number);
    updatePlateState(col, row);
  }
}

// ---------------------------------------------------
// 11) PLATE LOGIC (Unchanged)
// ---------------------------------------------------
function setupPlateLogic() {
  /* Primary logic is in updatePlateState triggered by events/timer */
}

function checkIfPlateShouldPress(col, row, plateEl) {
  const coordKey = `${col},${row}`;
  // For 'switch-plate' we can leave the logic as is (or adjust separately)
  if (plateEl.hasAttribute("switch-plate")) {
    if (startPosition.col === col && startPosition.row === row) return true;
    if (occupantMap[coordKey]) return true;
    return false;
  } else if (plateEl.hasAttribute("object-plate")) {
    let requiredConnects = plateEl.getAttribute("connect");
    if (!requiredConnects) return false;
    // Split the connect string into an array, e.g., ["roka", "kaja"]
    requiredConnects = requiredConnects.split(",").map(c => c.trim());
    
    // Query all entities on the same tile.
    const entitiesOnTile = document.querySelectorAll(
      `[data-map-coord="${coordKey}"]`
    );
    
    // Create a set to track which required connections we find.
    const foundConnections = new Set();
    
    for (const entity of entitiesOnTile) {
      // Check if the entity is one of your pickable objects
      if (entity.matches && entity.matches(".pickable-small")) {
        const entityConnect = entity.getAttribute("connect");
        if (entityConnect && requiredConnects.includes(entityConnect)) {
          foundConnections.add(entityConnect);
        }
      }
    }
    
    // The plate should be activated only if all required connections are present.
    return requiredConnects.every(conn => foundConnections.has(conn));
  }
  return false;
}

function triggerPlate(plateEntity, isPressing) {
  const actionData =
    plateEntity.getAttribute("switch-plate") ||
    plateEntity.getAttribute("object-plate");
  if (!actionData) {
    console.warn(
      `Plate ${plateEntity.getAttribute("data-map-coord")} triggered without action data.`
    );
    return;
  }
  // Split actions by semicolon (and remove extra spaces)
  const actions = actionData.split(";").map(a => a.trim()).filter(a => a);
  actions.forEach(action => {
    const match = action.match(
      /\(\s*(\d+)\s*,\s*(\d+)\s*\)\s*to\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)/
    );
    if (!match) {
      console.warn(`Plate action format error: "${action}"`);
      return;
    }
    const [, fromCol, fromRow, toCol, toRow] = match.map(Number);
    console.log(
      `Plate ${plateEntity.getAttribute("data-map-coord")} triggered (Pressing: ${isPressing}). Swapping (${fromCol},${fromRow}) <=> (${toCol},${toRow})`
    );
    if (isPressing) {
      swapTiles(fromCol, fromRow, toCol, toRow);
    } else {
      swapTiles(toCol, toRow, fromCol, fromRow);
    }
  });
}

function setupCombinedPlateCheck() {
  setInterval(() => {
    for (const key in plateMap) {
      const plate = plateMap[key];
      if (plate?.hasAttribute("object-plate")) {
        const [col, row] = key.split(",").map(Number);
        updatePlateState(col, row);
      }
    }
  }, 750);
}

function updatePlateState(col, row) {
  const coordKey = col + "," + row;
  const plate = plateMap[coordKey];
  if (!plate) {
    /* console.log(`No plate at ${coordKey}`); */ return;
  }
  const shouldBePressed = checkIfPlateShouldPress(col, row, plate);
  const isCurrentlyPressed = plate.__platePressed === true;
  if (shouldBePressed && !isCurrentlyPressed) {
    plate.__platePressed = true;
    triggerPlate(plate, true);
  } else if (!shouldBePressed && isCurrentlyPressed) {
    plate.__platePressed = false;
    triggerPlate(plate, false);
  }
}

// ---------------------------------------------------
// 12) RENDER CULLING (Unchanged)
// ---------------------------------------------------
function updateRenderCulling() {
  if (!cameraRig || renderDistance === Infinity) return;
  const camPos = cameraRig.getAttribute("position");
  const camCol = Math.round(camPos.x / (tileSize + tileSpacing));
  const camRow = Math.round(camPos.z / (tileSize + tileSpacing));
  document
    .querySelectorAll(
      "[data-map-coord]:not(.pickable-small):not(.pickable-large):not([follow])"
    )
    .forEach((tile) => {
      const coord = tile.getAttribute("data-map-coord")?.split(",");
      if (!coord || coord.length < 2) return;
      const col = parseInt(coord[0], 10),
        row = parseInt(coord[1], 10);
      if (isNaN(col) || isNaN(row)) return;
      const dist = Math.abs(col - camCol) + Math.abs(row - camRow);
      const targetVisible = dist <= renderDistance;
      if (tile.getAttribute("visible") !== String(targetVisible))
        tile.setAttribute("visible", targetVisible);
    });
  document
    .querySelectorAll(".pickable-small, .pickable-large, [follow]")
    .forEach((item) => {
      if (item.parentElement === cameraEl) {
        if (item.getAttribute("visible") !== "true")
          item.setAttribute("visible", "true");
        return;
      }
      const pos = item.getAttribute("position");
      if (!pos) return;
      const itemCol = Math.round(pos.x / (tileSize + tileSpacing));
      const itemRow = Math.round(pos.z / (tileSize + tileSpacing));
      if (isNaN(itemCol) || isNaN(itemRow)) return;
      const dist = Math.abs(itemCol - camCol) + Math.abs(itemRow - camRow);
      let isWithinDistance = dist <= renderDistance;
      let finalVisible = isWithinDistance;
      if (item.hasAttribute("animate")) {
        const followerId = item.getAttribute("animate");
        if (sceneEl.querySelector(`a-entity[follow="${followerId}"]`))
          finalVisible = false;
      } else if (item.hasAttribute("follow")) {
        if (isWithinDistance) {
          const targetId = item.getAttribute("follow");
          const target = findFollowTarget(targetId);
          if (!target || !target.parentElement) finalVisible = false;
        } else finalVisible = false;
      }
      if (item.getAttribute("visible") !== String(finalVisible)) {
        item.setAttribute("visible", finalVisible);
      }
    });
}

// ---------------------------------------------------
// 13) FOLLOW LOGIC & SYNCING (Unchanged)
// ---------------------------------------------------
function findFollowTarget(targetAnimateId) {
  if (!targetAnimateId) return null;
  const target = sceneEl.querySelector(
    `.pickable-small[animate="${targetAnimateId}"], .pickable-large[animate="${targetAnimateId}"]`
  );
  if (target && !target.parentElement) return null;
  return target;
}
function syncFollower(followerEl, knownTargetEl = null) {
  if (!followerEl?.hasAttribute("follow")) {
    return;
  }
  const targetAnimateId = followerEl.getAttribute("follow");
  if (!targetAnimateId) {
    if (followerEl.getAttribute("visible") !== "false")
      followerEl.setAttribute("visible", "false");
    return;
  }
  const targetEl =
    knownTargetEl instanceof Element
      ? knownTargetEl
      : findFollowTarget(targetAnimateId);
  if (targetEl && targetEl.parentElement) {
    if (
      knownTargetEl &&
      knownTargetEl.getAttribute("animate") !== targetAnimateId
    ) {
      console.warn(
        `syncFollower: knownTargetEl animate ID mismatch. Hiding follower ${targetAnimateId}.`
      );
      if (followerEl.getAttribute("visible") !== "false") {
        followerEl.setAttribute("visible", "false");
        followerEl.removeAttribute("animation__followpos");
      }
      return;
    }
    const targetPos = targetEl.object3D.position;
    if (targetPos) {
      followerEl.object3D.position.x = targetPos.x;
      followerEl.object3D.position.y = floorHeight;
      followerEl.object3D.position.z = targetPos.z;
    } else {
      console.warn(
        `syncFollower: Target element ${targetAnimateId} exists but has no valid position.`
      );
    }
    if (followerEl.getAttribute("visible") !== "true")
      followerEl.setAttribute("visible", "true");
    if (targetEl.getAttribute("visible") !== "false")
      targetEl.setAttribute("visible", "false");
    followerEl.removeAttribute("animation__followpos");
  } else {
    if (followerEl.getAttribute("visible") !== "false") {
      followerEl.setAttribute("visible", "false");
      followerEl.removeAttribute("animation__followpos");
    }
  }
}
function syncAllFollowers() {
  console.log("Syncing all followers...");
  const followers = sceneEl.querySelectorAll("a-entity[follow]");
  followers.forEach((follower) => {
    syncFollower(follower);
  });
  console.log(`Synced ${followers.length} followers.`);
}
function hideFollower(targetAnimateId) {
  if (!targetAnimateId) return;
  const follower = sceneEl.querySelector(
    `a-entity[follow="${targetAnimateId}"]`
  );
  if (follower) {
    follower.removeAttribute("animation__followpos");
    if (follower.getAttribute("visible") !== "false") {
      follower.setAttribute("visible", "false");
      console.log(`Hiding follower for animate ID: ${targetAnimateId}`);
    }
  }
}
function animateFollower(targetAnimateId, targetWorldPos, duration) {
  if (!targetAnimateId) return;
  const follower = sceneEl.querySelector(
    `a-entity[follow="${targetAnimateId}"]`
  );
  if (follower) {
    if (follower.getAttribute("visible") !== "true")
      follower.setAttribute("visible", "true");
    follower.removeAttribute("animation__followpos");
    const targetX = targetWorldPos.x;
    const targetZ = targetWorldPos.z;
    const targetY = floorHeight;
    follower.setAttribute("animation__followpos", {
      property: "position",
      to: `${targetX} ${targetY} ${targetZ}`,
      dur: duration,
      easing: "easeInOutQuad",
    });
  }
}
