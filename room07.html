<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Block Movement Example</title>
    <script src="https://aframe.io/releases/1.6.0/aframe.min.js"></script>
  </head>
  <body>
    <a-scene
      cursor="rayOrigin: mouse"
      fog="type: linear; color: #AAA; near: 0; far: 24"
    >
      <a-box
        id="exit-button"
        position="8 0 4"
        color="red"
        animation__move="property: position;
                   to: 8 -0.25 4;
                   dur: 1000;
                   easing: easeInOutQuad;
                   autoplay: false"
      ></a-box>

      <a-assets>
        <a-asset-item
          id="wall-a"
          src="https://cdn.glitch.global/1c490d13-0c87-48ec-aac0-3f731c19e451/box_wall.glb?v=1735939307042"
        ></a-asset-item>
        <a-asset-item
          id="wall-b"
          src="https://cdn.glitch.global/1c490d13-0c87-48ec-aac0-3f731c19e451/siena_2.glb?v=1737620362224"
        ></a-asset-item>

        <a-asset-item
          id="floor-1"
          src="https://cdn.glitch.global/1c490d13-0c87-48ec-aac0-3f731c19e451/grida.glb?v=1737620359998"
        ></a-asset-item>
        <a-asset-item
          id="floor-2"
          src="https://cdn.glitch.global/1c490d13-0c87-48ec-aac0-3f731c19e451/grida2.glb?v=1737620352807"
        ></a-asset-item>
      </a-assets>

      <a-sky color="lightblue"></a-sky>
    </a-scene>
    <script
      src="mainLogic.js"
      data-map="room07.txt"
      next-link="room08.html"
    ></script>
  </body>
</html>
